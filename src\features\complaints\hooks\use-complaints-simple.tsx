import { useUserWithProfile } from '@/hooks/use-auth';
import {
  checkPmaSubscriptionAccess,
  SubscriptionError,
} from '@/lib/subscription-utils';
import { supabase } from '@/lib/supabase';
import { useProjectContext } from '@/providers/project-context';
import type { Database } from '@/types/database';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { CreateComplaintInput } from '../schemas';

// Define the complaint data type with related tables for the query return
export type ComplaintWithRelations = {
  id: string;
  email: string;
  number: string;
  date: string;
  expected_completion_date: string | null;
  contractor_name: string | null;
  location: string | null;
  no_pma_lif: string | null;
  description: string | null;
  involves_mantrap: boolean | null;
  actual_completion_date: string | null;
  repair_completion_time: string | null;
  cause_of_damage: string | null;
  correction_action: string | null;
  proof_of_repair_urls: string[] | null;
  before_repair_files: string[] | null;
  repair_cost: number | null;
  status: Database['public']['Enums']['complaint_status'];
  follow_up: Database['public']['Enums']['complaint_follow_up'];
  verified_by: string | null;
  verified_date: string | null;
  created_at: string | null;
  updated_at: string | null;
  deleted_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  deleted_by: string | null;
  contractor_id: string | null;
  pma_id: string | null;
  project_id: string | null;
  contractors?: {
    id: string;
    name: string;
    contractor_type: string;
    hotline: string | null;
    code: string | null;
  } | null;
  users?: {
    id: string;
    name: string;
    email: string;
  } | null;
};

// Hook to fetch all complaints with related data filtered by selected project
export function useComplaints() {
  const { selectedProjectId } = useProjectContext();

  return useQuery({
    queryKey: ['complaints', selectedProjectId],
    queryFn: async (): Promise<ComplaintWithRelations[]> => {
      let query = supabase
        .from('complaints')
        .select(
          `
          *,
          contractors (
            id,
            name,
            contractor_type,
            hotline,
            code
          ),
          users!fk_complaints_created_by (
            id,
            name,
            email
          )
        `,
        )
        .is('deleted_at', null);

      // Filter by selected project if one is selected
      if (selectedProjectId) {
        query = query.eq('project_id', selectedProjectId);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;
      if (error) throw error;
      return (data || []) as ComplaintWithRelations[];
    },
    enabled: !!selectedProjectId, // Only fetch when a project is selected
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch ALL complaints for admin use (not filtered by project)
export function useAllComplaints() {
  return useQuery({
    queryKey: ['all-complaints'],
    queryFn: async (): Promise<ComplaintWithRelations[]> => {
      const { data, error } = await supabase
        .from('complaints')
        .select(
          `
          *,
          contractors (
            id,
            name,
            contractor_type,
            hotline,
            code
          ),
          users!fk_complaints_created_by (
            id,
            name,
            email
          )
        `,
        )
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return (data || []) as ComplaintWithRelations[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch a single complaint by ID
export function useComplaint(id: string) {
  return useQuery({
    queryKey: ['complaint', id],
    queryFn: async (): Promise<ComplaintWithRelations> => {
      const { data, error } = await supabase
        .from('complaints')
        .select(
          `
          *,
          contractors (
            id,
            name,
            contractor_type,
            hotline,
            code
          ),
          users!fk_complaints_created_by (
            id,
            name,
            email
          )
        `,
        )
        .eq('id', id)
        .is('deleted_at', null)
        .single();

      if (error) throw error;
      return data as ComplaintWithRelations;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create a new complaint
export function useCreateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const { selectedProjectId } = useProjectContext();

  return useMutation({
    mutationFn: async (
      data: CreateComplaintInput & {
        proofOfRepairFiles?: File[];
        beforeRepairFiles?: File[];
        beforeRepairUrls?: string[];
      },
    ) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to create complaints');
      }

      try {
        // Check PMA subscription access before proceeding
        if (data.pma_id) {
          const subscriptionCheck = await checkPmaSubscriptionAccess(
            data.pma_id,
          );

          if (!subscriptionCheck.hasAccess) {
            throw new SubscriptionError(
              subscriptionCheck.message,
              subscriptionCheck.status,
              subscriptionCheck.gracePeriodEnds,
            );
          }
        }

        // Use uploaded URLs if available, otherwise fallback to file names for proof of repair
        const proofFileUrls =
          data.proofOfRepairFiles?.map((file) => file.name) || [];
        const beforeRepairFileUrls = data.beforeRepairUrls || [];

        // Generate complaint number
        const { data: complaintNumber, error: numberError } =
          await supabase.rpc('generate_complaint_number');

        if (numberError) {
          throw new Error(
            `Failed to generate complaint number: ${numberError.message}`,
          );
        }

        if (!complaintNumber) {
          throw new Error('Failed to generate complaint number');
        }

        // Let the database trigger automatically generate the complaint number
        const complaintData: Database['public']['Tables']['complaints']['Insert'] =
          {
            email: data.email,
            date: data.date.toISOString().split('T')[0], // Convert to date string
            expected_completion_date: data.expected_completion_date
              .toISOString()
              .split('T')[0],
            contractor_name: data.contractor_name,
            location: data.location,
            no_pma_lif: data.no_pma_lif || '',
            description: data.description || 'No description provided',
            involves_mantrap: data.involves_mantrap || false,
            actual_completion_date: data.actual_completion_date
              ? data.actual_completion_date.toISOString().split('T')[0]
              : null,
            repair_completion_time: data.repair_completion_time || null,
            cause_of_damage: data.cause_of_damage || null,
            correction_action: data.correction_action || null,
            proof_of_repair_urls: proofFileUrls,
            before_repair_files: beforeRepairFileUrls,
            repair_cost: data.repair_cost || null,
            status: data.status || 'open',
            follow_up:
              'in_progress' as Database['public']['Enums']['complaint_follow_up'],
            project_id: selectedProjectId,
            pma_id: data.pma_id || null,
            created_by: user.id,
            number: complaintNumber,
          };

        const { data: result, error } = await supabase
          .from('complaints')
          .insert(complaintData)
          .select()
          .single();

        if (error) {
          throw new Error(`Database error: ${error.message} (${error.code})`);
        }

        return {
          ...result,
          proofFileUrls,
        };
      } catch (err) {
        throw err;
      }
    },
    onSuccess: (data) => {
      // Invalidate and refetch complaints for the current project
      queryClient.invalidateQueries({
        queryKey: ['complaints', selectedProjectId],
      });
      queryClient.invalidateQueries({ queryKey: ['all-complaints'] }); // Also invalidate admin queries

      // Invalidate dashboard stats to update the dashboard overview
      queryClient.invalidateQueries({
        queryKey: ['dashboard', 'complaints', selectedProjectId],
      });

      toast.success(
        `Complaint created successfully! Ticket ID: ${data.number}`,
      );
    },
    onError: (error: Error) => {
      let errorMessage = error?.message || 'Unknown error occurred';

      // Handle subscription errors specially
      if (error instanceof SubscriptionError) {
        if (error.status === 'grace_period' && error.gracePeriodEnds) {
          errorMessage = `${error.message}. Grace period ends: ${new Date(error.gracePeriodEnds).toLocaleDateString()}`;
        }
        toast.error(`Subscription Required: ${errorMessage}`);
      } else {
        toast.error(`Failed to create complaint: ${errorMessage}`);
      }
    },
  });
}

// Hook to update complaint verification status
export function useUpdateComplaintStatus() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const { selectedProjectId } = useProjectContext();

  return useMutation({
    mutationFn: async ({
      id,
      follow_up,
      verified_by,
      verified_date,
    }: {
      id: string;
      follow_up: Database['public']['Enums']['complaint_follow_up'];
      verified_by?: string;
      verified_date?: string;
    }) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to update complaints');
      }

      const updateData: Partial<
        Database['public']['Tables']['complaints']['Update']
      > = {
        follow_up,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      };

      // Add verification fields if provided
      if (verified_by !== undefined) {
        updateData.verified_by = verified_by;
      }
      if (verified_date !== undefined) {
        updateData.verified_date = verified_date;
      }

      const { data: result, error } = await supabase
        .from('complaints')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message} (${error.code})`);
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['complaints', selectedProjectId],
      });
      queryClient.invalidateQueries({ queryKey: ['all-complaints'] }); // Also invalidate admin queries
      queryClient.invalidateQueries({ queryKey: ['complaint'] });

      // Invalidate dashboard stats to update the dashboard overview
      queryClient.invalidateQueries({
        queryKey: ['dashboard', 'complaints', selectedProjectId],
      });

      toast.success('Complaint status updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(
        `Failed to update complaint status: ${error.message || 'Unknown error'}`,
      );
    },
  });
}

// Hook to update an existing complaint
export function useUpdateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();
  const { selectedProjectId } = useProjectContext();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: CreateComplaintInput & { proofOfRepairFiles?: File[] };
    }) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to update complaints');
      }

      // First, check if the complaint is verified - prevent editing verified complaints
      const { data: existingComplaint, error: fetchError } = await supabase
        .from('complaints')
        .select('follow_up')
        .eq('id', id)
        .single();

      if (fetchError) {
        throw new Error(`Failed to check complaint status: ${fetchError.message}`);
      }

      if (existingComplaint?.follow_up === 'verified') {
        throw new Error('Cannot edit a verified complaint. This complaint has been approved by an administrator.');
      }

      // TODO: File upload handling will be implemented later
      // For now, we'll store file names as placeholder
      const proofFileUrls =
        data.proofOfRepairFiles?.map((file) => file.name) || []; // Map UI status to database status
      const mapStatusToDb = (
        uiStatus?: string,
      ): Database['public']['Enums']['complaint_status'] => {
        switch (uiStatus) {
          case 'in_progress':
            return 'on_hold'; // Map to existing status for now
          case 'resolved':
            return 'closed'; // Map resolved to closed for now
          case 'pending_approval':
            return 'on_hold'; // Map to existing status for now
          case 'verified':
            return 'closed'; // Map verified to closed for now
          case 'closed':
            return 'closed';
          case 'open':
          default:
            return 'open';
        }
      };
      // Determine follow-up status based on section completion
      const sectionAComplete = !!(
        data.email &&
        data.date &&
        data.expected_completion_date &&
        data.contractor_name &&
        data.location &&
        data.no_pma_lif &&
        data.description
      );
      
      const sectionBComplete = !!(
        data.actual_completion_date &&
        data.repair_completion_time &&
        data.cause_of_damage &&
        data.correction_action &&
        (data.proof_of_repair_urls?.length || proofFileUrls.length)
      );
      
      // Determine follow-up status: preserve 'verified' if already set, otherwise compute based on completion
      let followUpStatus: Database['public']['Enums']['complaint_follow_up'] = 'in_progress';
      if (existingComplaint?.follow_up === 'verified') {
        followUpStatus = 'verified';
      } else if (sectionAComplete && sectionBComplete) {
        followUpStatus = 'pending_approval';
      } else if (sectionAComplete) {
        followUpStatus = 'in_progress';
      }

      const complaintData = {
        email: data.email,
        date: data.date?.toISOString().split('T')[0],
        expected_completion_date: data.expected_completion_date
          ?.toISOString()
          .split('T')[0],
        contractor_name: data.contractor_name,
        location: data.location,
        no_pma_lif: data.no_pma_lif || '',
        description: data.description || 'No description provided',
        involves_mantrap: data.involves_mantrap || false,
        actual_completion_date: data.actual_completion_date
          ? data.actual_completion_date.toISOString().split('T')[0]
          : null,
        repair_completion_time: data.repair_completion_time || null,
        cause_of_damage: data.cause_of_damage || null,
        correction_action: data.correction_action || null,
        proof_of_repair_urls: data.proof_of_repair_urls || proofFileUrls,
        repair_cost: data.repair_cost || null,
        status: mapStatusToDb(data.status) || 'open',
        follow_up: followUpStatus,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      };

      const { data: result, error } = await supabase
        .from('complaints')
        .update(complaintData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return {
        ...result,
        proofFileUrls,
      };
    },
    onSuccess: () => {
      // Invalidate and refetch complaints for the current project
      queryClient.invalidateQueries({
        queryKey: ['complaints', selectedProjectId],
      });
      queryClient.invalidateQueries({ queryKey: ['all-complaints'] }); // Also invalidate admin queries
      queryClient.invalidateQueries({ queryKey: ['complaint'] });
      toast.success('Complaint updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update complaint: ${error.message}`);
    },
  });
}

import type { Database } from '@/types/database';

export type PaymentStatus = Database['public']['Enums']['payment_status'];

export interface PaymentRecord {
  id: string;
  pma_subscription_id: string;
  billplz_bill_id: string | null;
  amount: number;
  status: PaymentStatus;
  paid_at: string | null;
  failure_reason: string | null;
  billplz_response: Record<string, unknown> | null;
  created_at: string;
}

export interface PaymentRecordWithSubscription extends PaymentRecord {
  pma_subscription: {
    id: string;
    contractor_id: string;
    pma_certificates: {
      id: string;
      pma_number: string;
      projects: {
        id: string;
        name: string;
      };
    };
  };
}

export interface PaymentRecordsFilters {
  status?: PaymentStatus;
  dateFrom?: string;
  dateTo?: string;
  subscriptionId?: string;
}

export interface PaymentRecordsResponse {
  records: PaymentRecordWithSubscription[];
  total: number;
  hasMore: boolean;
}

export interface PaymentRecordsQueryParams extends PaymentRecordsFilters {
  contractorId: string;
  page?: number;
  limit?: number;
}

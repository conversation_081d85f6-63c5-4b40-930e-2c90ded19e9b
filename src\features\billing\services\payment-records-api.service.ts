import { supabase } from '@/lib/supabase';
import type {
  PaymentRecordsQueryParams,
  PaymentRecordsResponse,
  PaymentRecordWithSubscription,
} from '../types/payment-records';

/**
 * Fetch payment records with filtering and pagination
 */
export async function getPaymentRecords(
  params: PaymentRecordsQueryParams,
): Promise<PaymentRecordsResponse> {
  const {
    contractorId,
    status,
    dateFrom,
    dateTo,
    subscriptionId,
    page = 1,
    limit = 50,
  } = params;

  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  // Build the optimized query - only select needed fields
  let query = supabase
    .from('pma_payment_records')
    .select(
      `
      id,
      pma_subscription_id,
      billplz_bill_id,
      amount,
      status,
      paid_at,
      failure_reason,
      created_at,
      pma_subscription:pma_subscriptions!inner(
        id,
        contractor_id,
        pma_certificates!inner(
          id,
          pma_number,
          projects!inner(
            id,
            name
          )
        )
      )
    `,
      { count: 'exact' },
    )
    .eq('pma_subscription.contractor_id', contractorId);

  // Apply filters early for better performance
  if (status) {
    query = query.eq('status', status);
  }

  if (dateFrom) {
    query = query.gte('created_at', dateFrom);
  }

  if (dateTo) {
    query = query.lte('created_at', dateTo);
  }

  if (subscriptionId) {
    query = query.eq('pma_subscription_id', subscriptionId);
  }

  // Apply ordering and pagination
  query = query
    .order('created_at', { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch payment records: ${error.message}`);
  }

  return {
    records: (data as unknown as PaymentRecordWithSubscription[]) || [],
    total: count || 0,
    hasMore: (data?.length || 0) === limit,
  };
}

/**
 * Fetch a single payment record by ID
 */
export async function getPaymentRecordById(
  id: string,
): Promise<PaymentRecordWithSubscription | null> {
  if (!id) {
    throw new Error('Payment record ID is required');
  }

  const { data, error } = await supabase
    .from('pma_payment_records')
    .select(
      `
      id,
      pma_subscription_id,
      billplz_bill_id,
      amount,
      status,
      paid_at,
      failure_reason,
      billplz_response,
      created_at,
      pma_subscription:pma_subscriptions!inner(
        id,
        contractor_id,
        pma_certificates!inner(
          id,
          pma_number,
          projects!inner(
            id,
            name
          )
        )
      )
    `,
    )
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(`Failed to fetch payment record: ${error.message}`);
  }

  return data as unknown as PaymentRecordWithSubscription;
}

/**
 * Get payment summary statistics for a contractor
 */
export async function getPaymentSummary(contractorId: string) {
  if (!contractorId) {
    throw new Error('Contractor ID is required');
  }

  const { data, error } = await supabase
    .from('pma_payment_records')
    .select(
      `
      status,
      amount,
      pma_subscription:pma_subscriptions!inner(
        contractor_id
      )
    `,
    )
    .eq('pma_subscription.contractor_id', contractorId);

  if (error) {
    throw new Error(`Failed to fetch payment summary: ${error.message}`);
  }

  const summary = {
    total: data?.length || 0,
    totalAmount: data?.reduce((sum, record) => sum + record.amount, 0) || 0,
    paid: data?.filter((record) => record.status === 'paid').length || 0,
    pending: data?.filter((record) => record.status === 'pending').length || 0,
    failed: data?.filter((record) => record.status === 'failed').length || 0,
    paidAmount:
      data
        ?.filter((record) => record.status === 'paid')
        .reduce((sum, record) => sum + record.amount, 0) || 0,
  };

  return summary;
}

/**
 * Get payment record data specifically for receipt generation
 * Only returns data for paid payments that can have receipts
 */
export async function getPaymentReceiptData(
  paymentId: string,
): Promise<PaymentRecordWithSubscription> {
  if (!paymentId) {
    throw new Error('Payment ID is required');
  }

  const paymentRecord = await getPaymentRecordById(paymentId);

  if (!paymentRecord) {
    throw new Error('Payment record not found');
  }

  if (paymentRecord.status !== 'paid') {
    throw new Error('Receipt not available for unpaid payments');
  }

  return paymentRecord;
}

/**
 * Get payment record data specifically for invoice generation
 * Works for all payment records regardless of status, uses created_at date
 */
export async function getPaymentInvoiceData(
  paymentId: string,
): Promise<PaymentRecordWithSubscription> {
  if (!paymentId) {
    throw new Error('Payment ID is required');
  }

  const paymentRecord = await getPaymentRecordById(paymentId);

  if (!paymentRecord) {
    throw new Error('Payment record not found');
  }

  return paymentRecord;
}

// Legacy class for backward compatibility (can be removed after refactoring)
export class PaymentRecordsApiService {
  async getPaymentRecords(
    params: PaymentRecordsQueryParams,
  ): Promise<PaymentRecordsResponse> {
    return getPaymentRecords(params);
  }

  async getPaymentRecordById(
    id: string,
  ): Promise<PaymentRecordWithSubscription | null> {
    return getPaymentRecordById(id);
  }

  async getPaymentSummary(contractorId: string) {
    return getPaymentSummary(contractorId);
  }
}

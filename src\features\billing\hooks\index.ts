// ================================
// PROJECT ACCESS HOOKS
// ================================

export {
  useAccessGuard,
  useCheckProjectAccess,
  useInvalidateProjectAccess,
  useMultipleProjectsAccess,
  type AccessDeniedReason,
  type AccessState,
} from './useProjectAccess';

// ================================
// PROJECT CREATION HOOKS
// ================================

export {
  useCanCreateProject,
  useCreateProjectWithBilling,
  useProjectCreationAnalytics,
  useProjectCreationCost,
  useProjectCreationFlow,
  useProjectCreationNavigation,
  type ProjectCreationFlowState,
  type ProjectCreationResult,
  type ProjectCreationStep,
  type ProjectWithBilling,
} from './useProjectCreation';

// ================================
// CONTRACTOR BULK PAYMENT HOOKS
// ================================

export {
  createContractorPaymentSummary,
  prepareContractorBulkPaymentData,
  useContractorBulkPayment,
} from './useContractorBulkPayment';

// ================================
// SAVED CARDS HOOKS
// ================================

export { useSavedCards } from './useSavedCards';

// ================================
// PAYMENT RECORDS HOOKS
// ================================

export {
  usePaymentRecords,
  usePaymentRecord,
  usePaymentSummary,
} from './usePaymentRecords';

// ================================
// COMMON PATTERNS & UTILITIES
// ================================

/**
 * Common query options for billing-related queries
 */
export const BILLING_QUERY_OPTIONS = {
  // Standard stale times for different types of data
  SUBSCRIPTION_STATUS: 30 * 1000, // 30 seconds
  PAYMENT_RECORDS: 2 * 60 * 1000, // 2 minutes
  PROJECT_ACCESS: 30 * 1000, // 30 seconds
  BILLING_STATS: 5 * 60 * 1000, // 5 minutes
  BILLPLZ_DATA: 2 * 60 * 1000, // 2 minutes

  // Refetch intervals
  PAYMENT_STATUS_POLLING: 60 * 1000, // 1 minute
  ACCESS_MONITORING: 60 * 1000, // 1 minute
} as const;

/**
 * Common query key patterns for consistency
 */
export const BILLING_QUERY_KEYS = {
  // Project access
  projectAccess: (
    projectId: string,
    contractorId?: string,
    userId?: string,
  ) => ['project-access', projectId, contractorId, userId],

  // PMA Subscriptions (new system)
  pmaSubscriptions: (contractorId: string) => [
    'pma-subscriptions',
    contractorId,
  ],
  pmaProjectSubscription: (projectId: string, contractorId: string) => [
    'pma-project-subscription',
    projectId,
    contractorId,
  ],

  // PMA Payment records
  pmaPaymentHistory: (
    subscriptionId: string,
    options?: Record<string, unknown>,
  ) => ['pma-payment-history', subscriptionId, ...(options ? [options] : [])],
  contractorPmaPaymentHistory: (
    contractorId: string,
    options?: Record<string, unknown>,
  ) => [
    'contractor-pma-payment-history',
    contractorId,
    ...(options ? [options] : []),
  ],

  // BillPlz
  billPlzBill: (billId: string) => ['billplz-bill-details', billId],
  billPlzPaymentStatus: (billId: string, paymentRecordId: string) => [
    'billplz-payment-status',
    billId,
    paymentRecordId,
  ],
} as const;

/**
 * Hook to get all billing-related query keys for invalidation
 */
export function useBillingQueryKeys(contractorId?: string, userId?: string) {
  return {
    // Invalidate all billing queries
    invalidateAll: () => [
      ['project-access'],
      ['pma-subscriptions'],
      ['pma-project-subscription'],
      ['pma-payment-history'],
      ['contractor-pma-payment-history'],
      ['billplz-bill-details'],
      ['billplz-payment-status'],
    ],

    // Invalidate contractor-specific queries
    invalidateContractor: (cId: string = contractorId || '') => [
      ['pma-subscriptions', cId],
      ['contractor-pma-payment-history', cId],
      ['pma-subscription-stats', cId],
      ['pma-payment-stats', cId],
    ],

    // Invalidate project-specific queries
    invalidateProject: (
      projectId: string,
      cId: string = contractorId || '',
    ) => [
      ['project-access', projectId],
      ['pma-project-subscription', projectId, cId],
    ],

    // Invalidate user access queries
    invalidateUserAccess: (uId: string = userId || '') => [
      ['project-access', undefined, undefined, uId],
      ['multiple-projects-access', undefined, undefined, uId],
    ],
  };
}

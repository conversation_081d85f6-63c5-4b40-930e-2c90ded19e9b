'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CheckCircle, Download, Eye } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';

interface PhotoViewerProps {
  photos: Array<{
    url: string;
    name: string;
  }>;
  title: string;
}

export function PhotoViewer({ photos, title }: PhotoViewerProps) {
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);

  const downloadFile = (fileUrl: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.click();
  };

  const isImageFile = (url: string) => {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
  };

  const isPdfFile = (url: string) => {
    return url.toLowerCase().includes('.pdf');
  };

  const getFileType = (url: string) => {
    if (isImageFile(url)) return 'Image file';
    if (isPdfFile(url)) return 'PDF file';
    return 'File';
  };

  const getPdfPreviewUrl = (url: string) => {
    // Use proxy API to ensure proper inline viewing with correct headers
    return `/api/preview-pdf?url=${encodeURIComponent(url)}`;
  };

  return (
    <>
      {/* Successfully uploaded files with green styling */}
      <div className="rounded-md border border-green-200 bg-green-50 overflow-hidden">
        {/* Success header */}
        <div className="px-3 py-2 bg-green-100 border-b border-green-200 flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <span className="text-sm font-medium text-green-800">Successfully Uploaded Files</span>
          <span className="text-xs text-green-600">({photos.length} file{photos.length !== 1 ? 's' : ''})</span>
        </div>
        <ul className="divide-y divide-green-200">
          {photos.map((photo, index) => (
            <li
              key={index}
              className="flex items-center justify-between p-3 gap-3 hover:bg-green-100 transition-colors"
            >
              <div className="flex items-center gap-3 min-w-0">
                <span className="text-xs text-green-600 w-6 shrink-0 font-medium">
                  {index + 1}.
                </span>
                <div className="min-w-0">
                  <p className="text-sm font-medium text-green-800 truncate">{photo.name}</p>
                  <p className="text-xs text-green-600">
                    {getFileType(photo.url)} • Successfully uploaded
                  </p>
                </div>
              </div>
              <div className="flex gap-1 sm:gap-2 shrink-0">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setSelectedPhoto(photo.url)}
                  title="View"
                  className="text-green-700 hover:text-green-800 hover:bg-green-200"
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => downloadFile(photo.url, photo.name)}
                  title="Download"
                  className="text-green-700 hover:text-green-800 hover:bg-green-200"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Photo Modal */}
      <Dialog
        open={!!selectedPhoto}
        onOpenChange={() => setSelectedPhoto(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {title}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedPhoto(null)}
              ></Button>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center space-y-4">
            {selectedPhoto && isImageFile(selectedPhoto) ? (
              <div className="relative w-full max-w-3xl">
                <Image
                  src={selectedPhoto}
                  alt="Photo preview"
                  width={800}
                  height={600}
                  className="w-full h-auto rounded-lg max-h-[70vh] object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="flex flex-col items-center space-y-4 p-8">
                          <div class="text-gray-500">
                            <svg class="h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </div>
                          <p class="text-gray-600">Failed to load image</p>
                        </div>
                      `;
                    }
                  }}
                />
              </div>
            ) : selectedPhoto && isPdfFile(selectedPhoto) ? (
              <div className="w-full max-w-4xl h-[70vh]">
                <iframe
                  src={getPdfPreviewUrl(selectedPhoto)}
                  className="w-full h-full border rounded-lg"
                  title="PDF Preview"
                />
              </div>
            ) : (
              <div className="flex flex-col items-center space-y-4 p-8">
                <div className="text-gray-500">
                  <Eye className="h-16 w-16" />
                </div>
                <p className="text-gray-600">
                  Preview not available for this file type
                </p>
              </div>
            )}
            <Button
              onClick={() => {
                if (selectedPhoto) {
                  const fileName =
                    photos.find((p) => p.url === selectedPhoto)?.name || 'file';
                  downloadFile(selectedPhoto, fileName);
                }
              }}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
